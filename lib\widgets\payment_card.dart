import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../models/custom_card_type.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/card_type_provider.dart';
import '../utils/number_formatter.dart';

class PaymentCard extends StatefulWidget {
  const PaymentCard({
    super.key,
    required this.payment,
    required this.customer,
    this.debt,
    this.onDelete,
    this.isMiniView = false,
    this.categoryColors,
    this.paymentCount,
    this.totalPayments,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onToggleSelection,
  });
  final Payment payment;
  final Customer customer;
  final Debt? debt;
  final VoidCallback? onDelete;
  final bool isMiniView;
  final List<Color>? categoryColors;
  final int? paymentCount; // رقم التسديد الحالي
  final int? totalPayments; // إجمالي عدد التسديدات
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onToggleSelection;

  @override
  State<PaymentCard> createState() => _PaymentCardState();
}

class _PaymentCardState extends State<PaymentCard> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    // تحديث العداد كل 30 ثانية للدقة العالية
    _timer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isSelectionMode
          ? widget.onToggleSelection
          : null, // في وضع التحديد: تبديل التحديد
      onLongPress: !widget.isSelectionMode
          ? widget.onToggleSelection
          : null, // خارج وضع التحديد: بدء التحديد المتعدد
      child: Container(
        margin: widget.isMiniView
            ? const EdgeInsets.all(1)
            : const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          gradient: widget.isSelected
              ? LinearGradient(
                  colors: [Colors.teal.shade100, Colors.white],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : LinearGradient(
                  colors: [Colors.teal.shade50, Colors.white],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
          borderRadius: BorderRadius.circular(widget.isMiniView ? 10 : 16),
          border: Border.all(
            color: widget.isSelected
                ? Colors.teal.shade400
                : Colors.grey.withValues(alpha: 0.2),
            width: widget.isSelected ? 2 : 1,
          ),
          boxShadow: [
            if (widget.isSelected)
              BoxShadow(
                color: Colors.teal.withValues(alpha: 0.2),
                blurRadius: widget.isMiniView ? 4 : 6,
                offset: Offset(0, widget.isMiniView ? 1 : 2),
              )
            else
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: widget.isMiniView ? 2 : 4,
                offset: Offset(0, widget.isMiniView ? 1 : 2),
              ),
          ],
        ),
        child: Column(
          children: [
            // شريط تمييز حالة السداد في الأعلى
            _buildPaymentStatusStrip(),

            // محتوى البطاقة
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(widget.isMiniView ? 6 : 16),
                child: Stack(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Time Counters at the top - يظهر في جميع الأوضاع
                        if (widget.debt != null) ...[
                          Container(
                            padding: EdgeInsets.all(widget.isMiniView ? 3 : 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(
                                  widget.isMiniView ? 8 : 12),
                              border: Border.all(
                                  color: Colors.green.shade200,
                                  width: widget.isMiniView ? 1 : 1.5),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.green.withValues(alpha: 0.1),
                                  blurRadius: widget.isMiniView ? 2 : 4,
                                  offset: Offset(0, widget.isMiniView ? 1 : 2),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: _buildTimeCounterWithSubtext(
                                    'استغرق الأيام',
                                    _getDaysFromEntryToPayment(),
                                    _getOverdueInfo(),
                                    Icons.event_available,
                                    Colors.blue,
                                  ),
                                ),
                                Container(
                                  width: 1,
                                  height: widget.isMiniView ? 20 : 40,
                                  color: Colors.green.shade200,
                                ),
                                Expanded(
                                  child: _buildTimeCounterWithSubtext(
                                    'منذ التسديد',
                                    widget.isMiniView
                                        ? _getDaysSincePaymentShort()
                                        : _getDaysSincePayment(),
                                    '',
                                    Icons.schedule,
                                    Colors.green,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: widget.isMiniView ? 4 : 12),
                        ],

                        // Header
                        Row(
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.inventory_2,
                                    size: widget.isMiniView ? 14 : 18,
                                    color: Colors.grey[600],
                                  ),
                                  SizedBox(width: widget.isMiniView ? 4 : 8),
                                  Text(
                                    widget.debt != null
                                        ? 'الكمية: ${NumberFormatter.formatNumber(widget.debt!.quantity)}'
                                        : 'تسديد',
                                    style: TextStyle(
                                      fontSize: widget.isMiniView ? 12 : 16,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF2C3E50),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // نوع الكارت مقابل الكمية (في المكان الأول)
                            if (widget.debt != null && widget.isMiniView) ...[
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 3),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: Colors.teal.withValues(alpha: 0.3),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.credit_card,
                                      size: 10,
                                      color: Colors.teal.shade600,
                                    ),
                                    const SizedBox(width: 3),
                                    Text(
                                      _getCardTypeInArabic(
                                          widget.debt!.cardType),
                                      style: const TextStyle(
                                        fontSize: 8,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black87,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 8),
                            ],
                          ],
                        ),

                        SizedBox(height: widget.isMiniView ? 4 : 12),

                        // Amount Info
                        Container(
                          padding: EdgeInsets.all(widget.isMiniView ? 8 : 12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(
                                widget.isMiniView ? 6 : 8),
                          ),
                          child: Column(
                            children: [
                              if (widget.debt != null) ...[
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'المبلغ الإجمالي:',
                                      style: TextStyle(
                                          fontSize: widget.isMiniView ? 10 : 14,
                                          color: Colors.black87),
                                    ),
                                    Text(
                                      NumberFormatter.formatCurrency(
                                          widget.debt?.amount ?? 0),
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                        fontSize: widget.isMiniView ? 12 : 16,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: widget.isMiniView ? 1 : 8),
                              ],
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'المبلغ المدفوع:',
                                    style: TextStyle(
                                        fontSize: widget.isMiniView ? 10 : 14,
                                        color: Colors.black87),
                                  ),
                                  Text(
                                    NumberFormatter.formatCurrency(
                                        widget.payment.amount),
                                    style: TextStyle(
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                      fontSize: widget.isMiniView ? 12 : 16,
                                    ),
                                  ),
                                ],
                              ),
                              if (widget.debt != null &&
                                  widget.payment.type ==
                                      PaymentType.partial) ...[
                                SizedBox(height: widget.isMiniView ? 2 : 8),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'المبلغ المتبقي:',
                                      style: TextStyle(
                                          fontSize: widget.isMiniView ? 9 : 14,
                                          color: Colors.black87),
                                    ),
                                    Text(
                                      NumberFormatter.formatCurrency(
                                        widget.debt?.remainingAmount ?? 0,
                                      ),
                                      style: TextStyle(
                                        color: Colors.orange,
                                        fontWeight: FontWeight.bold,
                                        fontSize: widget.isMiniView ? 10 : 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),

                        SizedBox(height: widget.isMiniView ? 3 : 12),

                        // بطاقة مدفوع (نُقلت للأسفل)
                        if (widget.isMiniView) ...[
                          Center(
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: widget.isMiniView ? 8 : 12,
                                vertical: widget.isMiniView ? 4 : 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(
                                    widget.isMiniView ? 8 : 12),
                                border: Border.all(
                                  color: Colors.green.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                'مدفوع',
                                style: TextStyle(
                                  color: Colors.green,
                                  fontSize: widget.isMiniView ? 10 : 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: widget.isMiniView ? 3 : 8),
                        ],

                        // Dates Section - كل تاريخ في صف منفصل
                        if (widget.debt != null) ...[
                          // تاريخ القيد
                          Container(
                            padding: EdgeInsets.all(widget.isMiniView ? 6 : 8),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(
                                  widget.isMiniView ? 8 : 12),
                              border: Border.all(
                                color: Colors.teal.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding:
                                      EdgeInsets.all(widget.isMiniView ? 4 : 6),
                                  decoration: BoxDecoration(
                                    color: Colors.teal.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(
                                        widget.isMiniView ? 6 : 8),
                                  ),
                                  child: Icon(
                                    Icons.event_note,
                                    size: widget.isMiniView ? 14 : 18,
                                    color: Colors.teal,
                                  ),
                                ),
                                SizedBox(width: widget.isMiniView ? 6 : 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'تاريخ القيد',
                                        style: TextStyle(
                                          fontSize: widget.isMiniView ? 9 : 12,
                                          color: Colors.black87,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      SizedBox(
                                          height: widget.isMiniView ? 2 : 4),
                                      Text(
                                        _formatDateArabic(
                                            widget.debt?.entryDate),
                                        style: TextStyle(
                                          fontSize: widget.isMiniView ? 9 : 12,
                                          color: Colors.black87,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        _formatTime(widget.debt?.entryDate ??
                                            DateTime.now()),
                                        style: TextStyle(
                                          fontSize: widget.isMiniView ? 7 : 10,
                                          color: Colors.black54,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: widget.isMiniView ? 4 : 6),

                          // تاريخ الاستحقاق
                          Container(
                            padding: EdgeInsets.all(widget.isMiniView ? 6 : 8),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(
                                  widget.isMiniView ? 8 : 12),
                              border: Border.all(
                                color: Colors.teal.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding:
                                      EdgeInsets.all(widget.isMiniView ? 4 : 6),
                                  decoration: BoxDecoration(
                                    color: Colors.teal.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(
                                        widget.isMiniView ? 6 : 8),
                                  ),
                                  child: Icon(
                                    Icons.schedule,
                                    size: widget.isMiniView ? 14 : 18,
                                    color: Colors.teal,
                                  ),
                                ),
                                SizedBox(width: widget.isMiniView ? 6 : 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'تاريخ الاستحقاق',
                                        style: TextStyle(
                                          fontSize: widget.isMiniView ? 9 : 12,
                                          color: Colors.black87,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      SizedBox(
                                          height: widget.isMiniView ? 2 : 4),
                                      Text(
                                        widget.debt?.dueDate != null
                                            ? _formatDateArabic(
                                                widget.debt?.dueDate)
                                            : 'لا يوجد تاريخ استحقاق',
                                        style: TextStyle(
                                          fontSize: widget.isMiniView ? 9 : 12,
                                          color: Colors.black87,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: widget.isMiniView ? 4 : 8),
                        ],

                        // Payment Notes
                        if (widget.payment.notes != null &&
                            widget.payment.notes!.isNotEmpty) ...[
                          SizedBox(height: widget.isMiniView ? 2 : 6),
                          Container(
                            padding: EdgeInsets.all(widget.isMiniView ? 4 : 8),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(
                                  widget.isMiniView ? 4 : 6),
                              border: Border.all(
                                color: Colors.teal.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding:
                                      EdgeInsets.all(widget.isMiniView ? 2 : 4),
                                  decoration: BoxDecoration(
                                    color: Colors.teal.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(
                                        widget.isMiniView ? 3 : 4),
                                  ),
                                  child: Icon(
                                    Icons.note_alt,
                                    size: widget.isMiniView ? 10 : 14,
                                    color: Colors.teal,
                                  ),
                                ),
                                SizedBox(width: widget.isMiniView ? 4 : 6),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        'ملاحظات:',
                                        style: TextStyle(
                                          fontSize: widget.isMiniView ? 8 : 10,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      SizedBox(
                                          height: widget.isMiniView ? 1 : 2),
                                      Flexible(
                                        child: Text(
                                          widget.payment.notes!,
                                          style: TextStyle(
                                            fontSize:
                                                widget.isMiniView ? 9 : 11,
                                            color: Colors.black87,
                                            fontWeight: FontWeight.w500,
                                            height: 1.2,
                                          ),
                                          maxLines:
                                              widget.isMiniView ? 3 : null,
                                          overflow: widget.isMiniView
                                              ? TextOverflow.ellipsis
                                              : null,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Spacer لدفع الأزرار إلى أسفل البطاقة
                        const Spacer(),

                        // Action Buttons - صف من الأزرار المنفصلة
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // زر التحرير
                            _buildActionButton(
                              icon: Icons.edit,
                              label: widget.isMiniView ? '' : 'تحرير',
                              color: Colors.blue,
                              onPressed: () => _editPayment(context),
                            ),

                            // زر الحذف
                            _buildActionButton(
                              icon: Icons.delete,
                              label: widget.isMiniView ? '' : 'حذف',
                              color: Colors.red,
                              onPressed: () =>
                                  _showDeletePaymentDialog(context),
                            ),

                            // زر الإرجاع
                            _buildActionButton(
                              icon: Icons.undo,
                              label: widget.isMiniView ? '' : 'ارجاع التسديد',
                              color: Colors.orange,
                              onPressed: () =>
                                  _showReversePaymentDialog(context),
                            ),

                            // زر التفاصيل
                            _buildActionButton(
                              icon: Icons.info_outline,
                              label: widget.isMiniView ? '' : 'تفاصيل',
                              color: Colors.blue.shade700,
                              onPressed: () =>
                                  _navigateToPaymentDetails(context),
                            ),
                          ],
                        ),
                      ],
                    ),

                    // أيقونة التحديد المحسنة
                    if (widget.isSelectionMode)
                      Positioned(
                        top: widget.isMiniView ? 6 : 8,
                        right: widget.isMiniView ? 6 : 8,
                        child: AnimatedScale(
                          scale: widget.isSelected ? 1.0 : 0.8,
                          duration: const Duration(milliseconds: 200),
                          child: Container(
                            width: widget.isMiniView ? 16 : 18,
                            height: widget.isMiniView ? 16 : 18,
                            decoration: BoxDecoration(
                              color: widget.isSelected
                                  ? Colors.teal.shade600
                                  : Colors.white,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: widget.isSelected
                                    ? Colors.teal.shade600
                                    : Colors.grey.shade400,
                                width: 1.5,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: widget.isSelected
                                ? Icon(
                                    Icons.done,
                                    color: Colors.white,
                                    size: widget.isMiniView ? 10 : 12,
                                  )
                                : null,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ), // Container
    ); // GestureDetector
  }

  // دالة لبناء شريط تمييز حالة السداد مع نوع الكارت
  Widget _buildPaymentStatusStrip() {
    // استخدام ألوان الفئة إذا كانت متوفرة، وإلا استخدام الأخضر الافتراضي
    final stripColor = widget.categoryColors?.isNotEmpty == true
        ? widget.categoryColors!.first
        : Colors.green;

    // طباعة للتصحيح
    debugPrint('PaymentCard: categoryColors = ${widget.categoryColors}');
    debugPrint('PaymentCard: stripColor = $stripColor');

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: widget.isMiniView ? 8 : 12,
        vertical: widget.isMiniView ? 4 : 6,
      ),
      decoration: BoxDecoration(
        color: stripColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          // أيقونة التسديد
          Icon(
            Icons.payment,
            color: Colors.white,
            size: widget.isMiniView ? 14 : 16,
          ),
          SizedBox(width: widget.isMiniView ? 4 : 6),

          // تاريخ التسديد
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // العنوان مع العداد
                Row(
                  children: [
                    Text(
                      'تاريخ التسديد',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: widget.isMiniView ? 8 : 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    // عرض عداد التسديدات إذا كان متوفراً
                    if (widget.paymentCount != null &&
                        widget.totalPayments != null) ...[
                      SizedBox(width: widget.isMiniView ? 4 : 6),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: widget.isMiniView ? 4 : 6,
                          vertical: widget.isMiniView ? 2 : 3,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius:
                              BorderRadius.circular(widget.isMiniView ? 6 : 8),
                        ),
                        child: Text(
                          '${widget.paymentCount}/${widget.totalPayments}',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: widget.isMiniView ? 7 : 9,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                SizedBox(height: widget.isMiniView ? 2 : 4),
                // التاريخ والوقت في نفس السطر
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        _formatDateArabic(widget.payment.paymentDate),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: widget.isMiniView ? 10 : 12,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(
                      _formatTime(widget.payment.paymentDate),
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: widget.isMiniView ? 8 : 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // إزالة عرض نوع الكارت من بطاقات التسديد حسب التفضيلات
        ],
      ),
    );
  }

  // دالة لبناء زر عمل منفصل
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: widget.isMiniView ? 1 : 2),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(widget.isMiniView ? 4 : 6),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: widget.isMiniView ? 3 : 6,
                vertical: widget.isMiniView ? 4 : 8,
              ),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(widget.isMiniView ? 4 : 6),
                border: Border.all(
                  color: color.withValues(alpha: 0.2),
                  width: 0.5,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    size: widget.isMiniView ? 12 : 16,
                    color: color,
                  ),
                  if (label.isNotEmpty && !widget.isMiniView) ...[
                    const SizedBox(height: 2),
                    Text(
                      label,
                      style: TextStyle(
                        fontSize: 8,
                        color: color,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // دالة لبناء عداد الوقت مع نص فرعي
  Widget _buildTimeCounterWithSubtext(
    String label,
    String value,
    String subtext,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: widget.isMiniView ? 12 : 16,
              color: color,
            ),
            SizedBox(width: widget.isMiniView ? 2 : 4),
            Text(
              label,
              style: TextStyle(
                fontSize: widget.isMiniView ? 8 : 10,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        SizedBox(height: widget.isMiniView ? 2 : 4),
        Text(
          value,
          style: TextStyle(
            fontSize: widget.isMiniView ? 12 : 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        if (subtext.isNotEmpty) ...[
          SizedBox(height: widget.isMiniView ? 1 : 2),
          Text(
            subtext,
            style: TextStyle(
              fontSize: widget.isMiniView ? 7 : 9,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  // حساب الأيام من تاريخ الإدخال إلى تاريخ التسديد
  String _getDaysFromEntryToPayment() {
    if (widget.debt == null) return '0';

    final entryDate = widget.debt?.entryDate ?? DateTime.now();
    final paymentDate = widget.payment.paymentDate;
    final difference = paymentDate.difference(entryDate).inDays;

    return difference.toString();
  }

  // معلومات التأخير
  String _getOverdueInfo() {
    if (widget.debt == null || widget.debt?.dueDate == null) return '';

    final dueDate = widget.debt!.dueDate;
    final paymentDate = widget.payment.paymentDate;
    final difference = paymentDate.difference(dueDate).inDays;

    if (difference > 0) {
      return 'متأخر $difference يوم';
    } else if (difference == 0) {
      return 'في الموعد';
    } else {
      return 'مبكر ${difference.abs()} يوم';
    }
  }

  // حساب الوقت المنقضي منذ التسديد (مفصل)
  String _getDaysSincePayment() {
    final now = DateTime.now();
    final paymentDate = widget.payment.paymentDate;

    // حساب الفرق بالأيام بناءً على التاريخ فقط (بدون الوقت)
    final today = DateTime(now.year, now.month, now.day);
    final paymentDay =
        DateTime(paymentDate.year, paymentDate.month, paymentDate.day);
    final daysDifference = today.difference(paymentDay).inDays;

    // Debug: طباعة التواريخ لمعرفة المشكلة
    debugPrint('=== Payment Date Debug ===');
    debugPrint('Payment ID: ${widget.payment.id}');
    debugPrint('Original Payment Date: $paymentDate');
    debugPrint('Payment Day (normalized): $paymentDay');
    debugPrint('Today (normalized): $today');
    debugPrint('Days Difference: $daysDifference');
    debugPrint('========================');

    if (daysDifference > 1) {
      return 'منذ $daysDifference يوم';
    } else if (daysDifference == 1) {
      return 'أمس';
    } else if (daysDifference == 0) {
      return 'اليوم';
    } else {
      // في حالة التسديد في المستقبل
      final futureDays = daysDifference.abs();
      return 'بعد $futureDays يوم';
    }
  }

  // نسخة مختصرة من حساب الوقت منذ التسديد
  String _getDaysSincePaymentShort() {
    final now = DateTime.now();
    final paymentDate = widget.payment.paymentDate;

    // حساب الفرق بالأيام بناءً على التاريخ فقط (بدون الوقت)
    final today = DateTime(now.year, now.month, now.day);
    final paymentDay =
        DateTime(paymentDate.year, paymentDate.month, paymentDate.day);
    final daysDifference = today.difference(paymentDay).inDays;

    if (daysDifference > 1) {
      return '$daysDifferenceد';
    } else if (daysDifference == 1) {
      return 'أمس';
    } else if (daysDifference == 0) {
      return 'اليوم';
    } else {
      // في حالة التسديد في المستقبل
      return 'مستقبلي';
    }
  }

  // تحرير التسديد
  void _editPayment(BuildContext context) {
    final amountController = TextEditingController(
      text: widget.payment.amount.toStringAsFixed(0),
    );
    final notesController = TextEditingController(
      text: widget.payment.notes ?? '',
    );
    final customerNameController = TextEditingController(
      text: widget.customer.name,
    );

    // متغير قابل للتغيير لتاريخ التسديد
    DateTime selectedPaymentDate = widget.payment.paymentDate;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.edit,
                  color: Colors.blue.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'تعديل التسديد',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // حقل اسم العميل
                const Text(
                  'اسم العميل:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: customerNameController,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                  ),
                  decoration: InputDecoration(
                    hintText: 'أدخل اسم العميل',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: Colors.blue.shade600, width: 2),
                    ),
                    prefixIcon: Icon(Icons.person, color: Colors.grey.shade600),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
                const SizedBox(height: 16),

                // حقل تاريخ التسديد
                const Text(
                  'تاريخ التسديد:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                InkWell(
                  onTap: () async {
                    final picked =
                        await _selectPaymentDate(context, selectedPaymentDate);
                    if (picked != null) {
                      setState(() {
                        selectedPaymentDate = picked;
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.calendar_today, color: Colors.grey.shade600),
                        const SizedBox(width: 12),
                        Text(
                          '${selectedPaymentDate.day}/${selectedPaymentDate.month}/${selectedPaymentDate.year}',
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.black87,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // حقل المبلغ
                const Text(
                  'المبلغ:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: amountController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  decoration: InputDecoration(
                    hintText: 'أدخل المبلغ',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: Colors.blue.shade600, width: 2),
                    ),
                    prefixIcon:
                        Icon(Icons.attach_money, color: Colors.grey.shade600),
                    suffixText: 'د.ع',
                    suffixStyle: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
                const SizedBox(height: 16),

                // حقل الملاحظات
                const Text(
                  'الملاحظات:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: notesController,
                  maxLines: 3,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                  ),
                  decoration: InputDecoration(
                    hintText: 'أدخل ملاحظات (اختياري)',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: Colors.blue.shade600, width: 2),
                    ),
                    prefixIcon: Icon(Icons.note, color: Colors.grey.shade600),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                amountController.dispose();
                notesController.dispose();
                customerNameController.dispose();
                Navigator.pop(context);
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey.shade700,
                backgroundColor: Colors.grey.shade100,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  final newAmount = double.tryParse(amountController.text);
                  if (newAmount == null || newAmount <= 0) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى إدخال مبلغ صحيح'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                    return;
                  }

                  // حفظ المراجع قبل العمليات غير المتزامنة
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  final debtProvider =
                      Provider.of<DebtProvider>(context, listen: false);
                  final customerProvider =
                      Provider.of<CustomerProvider>(context, listen: false);

                  final updatedPayment = widget.payment.copyWith(
                    amount: newAmount,
                    notes: notesController.text.isEmpty
                        ? null
                        : notesController.text,
                    paymentDate: selectedPaymentDate,
                    updatedAt: DateTime.now(),
                  );

                  // تحديث اسم العميل إذا تغير
                  if (customerNameController.text.trim() !=
                      widget.customer.name) {
                    final updatedCustomer = widget.customer.copyWith(
                      name: customerNameController.text.trim(),
                    );
                    await customerProvider.updateCustomer(updatedCustomer);
                  }

                  // تحديث التسديد
                  await debtProvider.updatePayment(updatedPayment);

                  // تنظيف الموارد
                  amountController.dispose();
                  notesController.dispose();
                  customerNameController.dispose();

                  // إغلاق النافذة
                  navigator.pop();

                  // عرض رسالة النجاح
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Row(
                        children: [
                          Icon(Icons.check_circle,
                              color: Colors.white, size: 20),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'تم تعديل التسديد بنجاح',
                              style: TextStyle(fontWeight: FontWeight.w500),
                            ),
                          ),
                        ],
                      ),
                      backgroundColor: Colors.green,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                } catch (e) {
                  // تنظيف الموارد في حالة الخطأ
                  amountController.dispose();
                  notesController.dispose();
                  customerNameController.dispose();

                  if (context.mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            const Icon(Icons.error,
                                color: Colors.white, size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'حدث خطأ: $e',
                                style: const TextStyle(
                                    fontWeight: FontWeight.w500),
                              ),
                            ),
                          ],
                        ),
                        backgroundColor: Colors.red,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                elevation: 2,
                shadowColor: Colors.blue.shade200,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.save, size: 18),
                  SizedBox(width: 8),
                  Text(
                    'حفظ التعديل',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // حذف التسديد
  void _showDeletePaymentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التسديد'),
        content: const Text('هل أنت متأكد من حذف هذا التسديد؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                if (widget.payment.id != null) {
                  final debtProvider =
                      Provider.of<DebtProvider>(context, listen: false);
                  await debtProvider.deletePayment(widget.payment.id!);

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم حذف التسديد بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // إرجاع التسديد
  void _showReversePaymentDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.undo,
                color: Colors.orange.shade700,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'إرجاع التسديد',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'هل أنت متأكد من إرجاع هذا التسديد؟',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.orange.shade700,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'ما سيحدث:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• سيتم حذف سجل التسديد نهائياً',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.black87,
                    ),
                  ),
                  const Text(
                    '• سيتم إرجاع المبلغ إلى الدين',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.black87,
                    ),
                  ),
                  const Text(
                    '• سيتم تحديث حالة الدين تلقائياً',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                if (widget.payment.id != null) {
                  final debtProvider =
                      Provider.of<DebtProvider>(context, listen: false);
                  await debtProvider.reversePayment(widget.payment.id!);

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'تم إرجاع التسديد بنجاح',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        backgroundColor: Colors.orange.shade600,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    );
                  }
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const Icon(
                            Icons.error,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'حدث خطأ: $e',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      backgroundColor: Colors.red.shade600,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'إرجاع التسديد',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // اختيار تاريخ التسديد
  Future<DateTime?> _selectPaymentDate(
      BuildContext context, DateTime currentDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.fromSeed(
              seedColor: Colors.blue,
              brightness: Theme.of(context).brightness,
            ),
          ),
          child: child!,
        );
      },
    );

    return picked;
  }

  // الانتقال إلى تفاصيل التسديد
  void _navigateToPaymentDetails(BuildContext context) {
    // TODO: تنفيذ الانتقال إلى تفاصيل التسديد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تفاصيل التسديد قريباً')),
    );
  }

  // تنسيق التاريخ بالعربية
  String _formatDateArabic(DateTime? date) {
    if (date == null) return '';

    final dayFormat = DateFormat('EEEE', 'ar');
    final dateFormat = DateFormat('yyyy/MM/dd'); // السنة على اليسار

    return '${dayFormat.format(date)} - ${dateFormat.format(date)}';
  }

  // تنسيق الوقت بالعربية (صباحاً/مساءً)
  String _formatTime(DateTime date) {
    final hour = date.hour;
    final minute = date.minute;

    // تحديد الفترة
    String period;
    int displayHour;

    if (hour == 0) {
      displayHour = 12;
      period = 'صباحاً';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'صباحاً';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'مساءً';
    } else {
      displayHour = hour - 12;
      period = 'مساءً';
    }

    // تنسيق الدقائق
    final minuteStr = minute.toString().padLeft(2, '0');

    return '$displayHour:$minuteStr $period';
  }

  // دالة تحويل نوع الكارت إلى اسم عربي
  String _getCardTypeInArabic(String cardTypeId) {
    final cardTypeProvider =
        Provider.of<CardTypeProvider>(context, listen: false);

    // إزالة كلمة "متعدد:" إذا كانت موجودة
    String cleanCardTypeId = cardTypeId;
    if (cardTypeId.startsWith('متعدد: ')) {
      cleanCardTypeId = cardTypeId.substring(7); // إزالة "متعدد: "

      // إذا كان النص يحتوي على عدة أنواع مفصولة بفاصلة، عرض جميع الأنواع
      if (cleanCardTypeId.contains(', ')) {
        final cardTypes = cleanCardTypeId.split(', ');
        final displayNames = cardTypes.map((cardType) {
          // إزالة الأقواس والأرقام إذا كانت موجودة
          final cleanType =
              cardType.replaceAll(RegExp(r'\s*\(\d+\)'), '').trim();
          return _getSingleCardTypeDisplayName(cleanType, cardTypeProvider);
        }).toList();
        return displayNames.join(' + '); // دمج الأسماء بعلامة +
      }
    }

    return _getSingleCardTypeDisplayName(cleanCardTypeId, cardTypeProvider);
  }

  // دالة مساعدة للحصول على اسم نوع كارت واحد
  String _getSingleCardTypeDisplayName(
    String cardTypeId,
    CardTypeProvider cardTypeProvider,
  ) {
    // البحث في الأنواع الافتراضية أولاً
    try {
      final defaultType = CardType.values.firstWhere(
        (type) => type.name == cardTypeId,
      );
      return defaultType.displayName;
    } catch (e) {
      // إذا لم يوجد في الأنواع الافتراضية، ابحث في الأنواع المخصصة
      try {
        final customType = cardTypeProvider.customCardTypes.firstWhere(
          (type) => 'custom_${type.id}' == cardTypeId,
        );
        return customType.displayName;
      } catch (e) {
        // إذا لم يوجد في أي مكان، أرجع الـ ID كما هو
        return cardTypeId;
      }
    }
  }
}

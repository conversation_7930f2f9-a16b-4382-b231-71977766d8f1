import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../providers/debt_provider.dart';
import '../widgets/debt_card.dart';
import '../widgets/payment_card.dart';

class AccountStatementScreen extends StatefulWidget {
  const AccountStatementScreen({super.key, required this.customer});
  final Customer customer;

  @override
  State<AccountStatementScreen> createState() => _AccountStatementScreenState();
}

class _AccountStatementScreenState extends State<AccountStatementScreen>
    with SingleTickerProviderStateMixin {
  DateTime? _startDate;
  DateTime? _endDate;
  String _selectedPeriod = 'الكل';
  bool _isLoading = false;

  // متحكم التبويب
  late TabController _tabController;

  final List<String> _periods = [
    'الكل',
    'آخر 7 أيام',
    'آخر 30 يوم',
    'آخر 3 أشهر',
    'آخر 6 أشهر',
    'السنة الحالية',
    'فترة مخصصة',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
    // تحميل التسديدات عند بدء الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DebtProvider>(context, listen: false)
          .loadCustomerPayments(widget.customer.id!);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    final debtProvider = Provider.of<DebtProvider>(context, listen: false);

    await Future.wait([
      debtProvider.loadCustomerDebts(widget.customer.id!),
      debtProvider.loadCustomerPayments(widget.customer.id!),
    ]);

    setState(() => _isLoading = false);
  }

  void _updateDateRange() {
    final now = DateTime.now();

    switch (_selectedPeriod) {
      case 'آخر 7 أيام':
        _startDate = now.subtract(const Duration(days: 7));
        _endDate = now;
        break;
      case 'آخر 30 يوم':
        _startDate = now.subtract(const Duration(days: 30));
        _endDate = now;
        break;
      case 'آخر 3 أشهر':
        _startDate = DateTime(now.year, now.month - 3, now.day);
        _endDate = now;
        break;
      case 'آخر 6 أشهر':
        _startDate = DateTime(now.year, now.month - 6, now.day);
        _endDate = now;
        break;
      case 'السنة الحالية':
        _startDate = DateTime(now.year);
        _endDate = now;
        break;
      case 'الكل':
        _startDate = null;
        _endDate = null;
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildUnpaidDebtsTab(),
                _buildPaidDebtsTab(),
                _buildPaymentsTab(),
              ],
            ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'كشف الحساب',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: Colors.teal.shade700,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: _exportStatement,
          icon: const Icon(Icons.share),
          tooltip: 'مشاركة كشف الحساب',
        ),
        IconButton(
          onPressed: _printStatement,
          icon: const Icon(Icons.print),
          tooltip: 'طباعة كشف الحساب',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
        tabs: const [
          Tab(
            icon: Icon(Icons.pending_actions, size: 20),
            text: 'غير مسدد',
          ),
          Tab(
            icon: Icon(Icons.check_circle, size: 20),
            text: 'مسدد',
          ),
          Tab(
            icon: Icon(Icons.payment, size: 20),
            text: 'التسديدات',
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.teal.shade600, Colors.teal.shade800],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.teal.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header Section
          Container(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.25),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet_outlined,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'كشف حساب العميل',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.95),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        widget.customer.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Statistics Section
          Container(
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
            child: _buildSummaryCards(),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == widget.customer.id)
            .toList();

        final customerPayments = debtProvider.payments
            .where((payment) => payment.customerId == widget.customer.id)
            .toList();

        // تطبيق فلتر التاريخ
        final filteredDebts = _filterByDate(customerDebts);
        final filteredPayments = _filterByDate(customerPayments);

        final totalDebts = filteredDebts.fold<double>(
          0,
          (sum, debt) => sum + debt.amount,
        );
        final totalPayments = filteredPayments.fold<double>(
          0,
          (sum, payment) => sum + payment.amount,
        );
        final balance = totalDebts - totalPayments;

        // حساب عدد الكارتات بناءً على المبلغ مقسوماً على قيمة الكارت الواحد
        double totalCardsFromAmount = 0;
        for (final debt in filteredDebts) {
          if (debt.remainingAmount > 0) {
            // حساب قيمة الكارت الواحد (المبلغ الأصلي ÷ الكمية)
            final cardValue =
                debt.quantity > 0 ? debt.amount / debt.quantity : 0;
            if (cardValue > 0) {
              // حساب عدد الكارتات من المبلغ المتبقي
              totalCardsFromAmount += debt.remainingAmount / cardValue;
            }
          }
        }
        final totalCards = totalCardsFromAmount.round();

        return Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCompactStatCard(
                  'إجمالي الديون',
                  NumberFormat('#,##0').format(totalDebts.round()),
                  Icons.trending_up,
                  Colors.red,
                ),
                const SizedBox(width: 6),
                _buildCompactStatCard(
                  'المدفوعات',
                  NumberFormat('#,##0').format(totalPayments.round()),
                  Icons.trending_down,
                  Colors.green,
                ),
                const SizedBox(width: 6),
                _buildCompactStatCard(
                  'الغير مسدد',
                  NumberFormat('#,##0').format(balance.round()),
                  Icons.pending_actions,
                  balance >= 0 ? Colors.orange : Colors.green,
                ),
                const SizedBox(width: 6),
                _buildCompactStatCard(
                  'عدد الكارت',
                  NumberFormat('#,##0').format(totalCards),
                  Icons.credit_card,
                  Colors.blue,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // البطاقة الرئيسية العريضة
  Widget _buildMainSummaryCard(
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.25),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withValues(alpha: 0.5)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(icon, color: Colors.white, size: 32),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.95),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    NumberFormat('#,##0.00').format(amount),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // البطاقات الصغيرة
  Widget _buildSummaryCard(
    String title,
    double amount,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withValues(alpha: 0.4)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: Colors.white, size: 24),
          ),
          const SizedBox(height: 12),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              NumberFormat('#,##0.00').format(amount),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.95),
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  List<T> _filterByDate<T>(List<T> items) {
    if (_startDate == null || _endDate == null) return items;

    return items.where((item) {
      DateTime itemDate;
      if (item is Debt) {
        itemDate = item.entryDate;
      } else if (item is Payment) {
        itemDate = item.paymentDate;
      } else {
        return true;
      }

      return itemDate.isAfter(_startDate!.subtract(const Duration(days: 1))) &&
          itemDate.isBefore(_endDate!.add(const Duration(days: 1)));
    }).toList();
  }

  void _exportStatement() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة التصدير قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _printStatement() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة الطباعة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.12),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'فترة كشف الحساب:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 40,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _periods.map((period) {
                  final isSelected = _selectedPeriod == period;
                  return Padding(
                    padding: const EdgeInsets.only(left: 8),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedPeriod = period;
                          if (period == 'فترة مخصصة') {
                            _showCustomDatePicker();
                          } else {
                            _updateDateRange();
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: isSelected
                              ? LinearGradient(
                                  colors: [
                                    Colors.blue.shade400,
                                    Colors.blue.shade600,
                                  ],
                                )
                              : null,
                          color: isSelected ? null : Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected
                                ? Colors.transparent
                                : Colors.grey.shade300,
                          ),
                        ),
                        child: Text(
                          period,
                          style: TextStyle(
                            color: isSelected
                                ? Colors.white
                                : Colors.grey.shade700,
                            fontWeight:
                                isSelected ? FontWeight.bold : FontWeight.w500,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          if (_startDate != null && _endDate != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.date_range, color: Colors.blue.shade600, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'من ${DateFormat('dd/MM/yyyy').format(_startDate!)} إلى ${DateFormat('dd/MM/yyyy').format(_endDate!)}',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _showCustomDatePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
      helpText: 'اختر فترة كشف الحساب',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
      saveText: 'حفظ',
      errorFormatText: 'تنسيق التاريخ غير صحيح',
      errorInvalidText: 'التاريخ غير صالح',
      errorInvalidRangeText: 'نطاق التاريخ غير صالح',
      fieldStartHintText: 'تاريخ البداية',
      fieldEndHintText: 'تاريخ النهاية',
      fieldStartLabelText: 'من تاريخ',
      fieldEndLabelText: 'إلى تاريخ',
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.blue.shade600,
              onSurface: Colors.black87,
              surfaceContainerHighest: Colors.grey.shade100,
              onSurfaceVariant: Colors.black54,
              outline: Colors.blue.shade300,
            ),
            textTheme: Theme.of(context).textTheme.copyWith(
                  headlineSmall: const TextStyle(
                    color: Colors.black87,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  bodyLarge:
                      const TextStyle(color: Colors.black87, fontSize: 16),
                  bodyMedium:
                      const TextStyle(color: Colors.black54, fontSize: 14),
                  labelLarge: TextStyle(
                    color: Colors.blue.shade600,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
            dialogTheme: DialogThemeData(
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            inputDecorationTheme: InputDecorationTheme(
              filled: true,
              fillColor: Colors.grey.shade50,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue.shade600, width: 2),
              ),
              labelStyle: TextStyle(color: Colors.grey.shade700, fontSize: 14),
              hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  Widget _buildStatementContent_UNUSED() {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == widget.customer.id)
            .toList();

        final customerPayments = debtProvider.payments
            .where((payment) => payment.customerId == widget.customer.id)
            .toList();

        // تطبيق فلتر التاريخ
        final filteredDebts = _filterByDate(customerDebts);
        final filteredPayments = _filterByDate(customerPayments);

        // تصنيف الديون حسب حالة التسديد
        final paidDebts =
            filteredDebts.where((debt) => debt.remainingAmount == 0).toList();
        final unpaidDebts =
            filteredDebts.where((debt) => debt.remainingAmount > 0).toList();

        // دمج وترتيب العمليات حسب التاريخ
        final allTransactions = <Map<String, dynamic>>[];

        for (final debt in filteredDebts) {
          allTransactions.add({
            'type': 'debt',
            'date': debt.entryDate,
            'data': debt,
          });
        }

        for (final payment in filteredPayments) {
          allTransactions.add({
            'type': 'payment',
            'date': payment.paymentDate,
            'data': payment,
          });
        }

        allTransactions.sort(
          (a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime),
        );

        if (allTransactions.isEmpty) {
          return _buildEmptyState();
        }

        return Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.history, color: Colors.blue.shade600, size: 24),
                    const SizedBox(width: 12),
                    const Text(
                      'تاريخ العمليات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade100,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.blue.shade300),
                      ),
                      child: Text(
                        '${allTransactions.length} عملية',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عرض الديون غير المسددة
                    if (unpaidDebts.isNotEmpty) ...[
                      _buildSectionHeader(
                          'الديون غير المسددة', unpaidDebts.length, Colors.red),
                      ...unpaidDebts.map((debt) => Padding(
                            padding: const EdgeInsets.only(bottom: 6),
                            child: DebtCard(
                              debt: debt,
                              disableLongPress: true,
                            ),
                          )),
                      const SizedBox(height: 16),
                    ],

                    // عرض الديون المسددة
                    if (paidDebts.isNotEmpty) ...[
                      _buildSectionHeader(
                          'الديون المسددة', paidDebts.length, Colors.green),
                      ...paidDebts.map((debt) => Padding(
                            padding: const EdgeInsets.only(bottom: 6),
                            child: DebtCard(
                              debt: debt,
                              disableLongPress: true,
                            ),
                          )),
                      const SizedBox(height: 16),
                    ],

                    // عرض التسديدات
                    if (filteredPayments.isNotEmpty) ...[
                      _buildSectionHeader(
                          'التسديدات', filteredPayments.length, Colors.blue),
                      ...filteredPayments.map((payment) => Padding(
                            padding: const EdgeInsets.only(bottom: 6),
                            child: PaymentCard(
                              payment: payment,
                              customer: widget.customer,
                            ),
                          )),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(
                  Icons.receipt_long_outlined,
                  size: 60,
                  color: Colors.grey.shade400,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'لا توجد عمليات في هذه الفترة',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'جرب تغيير فترة البحث أو إضافة عمليات جديدة',
                style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _selectedPeriod = 'الكل';
                    _startDate = null;
                    _endDate = null;
                  });
                },
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('عرض جميع العمليات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء قسم الإحصائيات
  Widget _buildStatisticsSection(
      List<Debt> filteredDebts, List<Payment> filteredPayments) {
    final totalDebts = filteredDebts.fold<double>(
      0,
      (sum, debt) => sum + debt.amount,
    );
    final totalPayments = filteredPayments.fold<double>(
      0,
      (sum, payment) => sum + payment.amount,
    );
    final balance = totalDebts - totalPayments;

    return Container(
      height: 80,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildCompactStatCard(
              'إجمالي الديون',
              NumberFormat('#,##0').format(totalDebts.round()),
              Icons.trending_up,
              Colors.red,
            ),
            const SizedBox(width: 6),
            _buildCompactStatCard(
              'المدفوعات',
              NumberFormat('#,##0').format(totalPayments.round()),
              Icons.trending_down,
              Colors.green,
            ),
            const SizedBox(width: 6),
            _buildCompactStatCard(
              'الرصيد',
              NumberFormat('#,##0').format(balance.round()),
              Icons.account_balance,
              balance >= 0 ? Colors.orange : Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة إحصائية مدمجة
  Widget _buildCompactStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      constraints: const BoxConstraints(minWidth: 100, maxWidth: 140),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.grey.shade50, Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(height: 3),
          FittedBox(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 9,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // بناء تبويب الديون غير المسددة
  Widget _buildUnpaidDebtsTab() {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == widget.customer.id)
            .toList();

        final filteredDebts = _filterByDate(customerDebts);
        final unpaidDebts =
            filteredDebts.where((debt) => debt.remainingAmount > 0).toList();

        return CustomScrollView(
          slivers: [
            // الهيدر والإحصائيات
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildPeriodSelector(),
                ],
              ),
            ),
            // قائمة الديون غير المسددة
            if (unpaidDebts.isEmpty)
              SliverFillRemaining(
                child: _buildEmptyTabState(
                    'لا توجد ديون غير مسددة', Icons.check_circle),
              )
            else
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: DebtCard(
                          debt: unpaidDebts[index],
                          disableLongPress: true,
                        ),
                      );
                    },
                    childCount: unpaidDebts.length,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  // بناء تبويب الديون المسددة
  Widget _buildPaidDebtsTab() {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == widget.customer.id)
            .toList();

        final filteredDebts = _filterByDate(customerDebts);
        final paidDebts =
            filteredDebts.where((debt) => debt.remainingAmount == 0).toList();

        return CustomScrollView(
          slivers: [
            // الهيدر والإحصائيات
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildPeriodSelector(),
                ],
              ),
            ),
            // قائمة الديون المسددة
            if (paidDebts.isEmpty)
              SliverFillRemaining(
                child: _buildEmptyTabState(
                    'لا توجد ديون مسددة', Icons.pending_actions),
              )
            else
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: DebtCard(
                          debt: paidDebts[index],
                          disableLongPress: true,
                        ),
                      );
                    },
                    childCount: paidDebts.length,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  // بناء تبويب التسديدات
  Widget _buildPaymentsTab() {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerPayments = debtProvider.payments
            .where((payment) => payment.customerId == widget.customer.id)
            .toList();

        final filteredPayments = _filterByDate(customerPayments);

        return CustomScrollView(
          slivers: [
            // الهيدر والإحصائيات
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildPeriodSelector(),
                ],
              ),
            ),
            // قائمة التسديدات
            if (filteredPayments.isEmpty)
              SliverFillRemaining(
                child: _buildEmptyTabState('لا توجد تسديدات', Icons.payment),
              )
            else
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: PaymentCard(
                          payment: filteredPayments[index],
                          customer: widget.customer,
                        ),
                      );
                    },
                    childCount: filteredPayments.length,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  // بناء حالة فارغة للتبويب
  Widget _buildEmptyTabState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              icon,
              size: 48,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // بناء عنوان القسم
  Widget _buildSectionHeader(String title, int count, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12, top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            title.contains('غير المسددة')
                ? Icons.pending_actions
                : title.contains('المسددة')
                    ? Icons.check_circle
                    : Icons.payment,
            color: color,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '$count',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

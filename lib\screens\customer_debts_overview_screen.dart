import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/custom_card_type.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';

import 'package:intl/intl.dart';

class CustomerDebtsOverviewScreen extends StatefulWidget {
  const CustomerDebtsOverviewScreen({
    super.key,
    required this.customer,
  });
  final Customer customer;

  @override
  State<CustomerDebtsOverviewScreen> createState() =>
      _CustomerDebtsOverviewScreenState();
}

class _CustomerDebtsOverviewScreenState
    extends State<CustomerDebtsOverviewScreen> {
  final String _currentViewType = 'table'; // table only
  String _currentFilter =
      'all'; // all, today, yesterday, soon, thisWeek, thisMonth, overdue

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(
              Icons.person,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'بطاقات ${widget.customer.name}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // أزرار الفلترة في الشريط العلوي
          _buildFilterChips(),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(50),
          child: Container(
            color: const Color(0xFF2E7D32),
            padding: const EdgeInsets.only(bottom: 8, left: 8, right: 8),
            child: _buildTopFilterBar(),
          ),
        ),
      ),
      body: Consumer<DebtProvider>(
        builder: (context, debtProvider, child) {
          // فلترة الديون للعميل المحدد
          final customerDebts = debtProvider.debts
              .where((debt) => debt.customerId == widget.customer.id)
              .toList();

          // تطبيق الفلتر المحدد
          final filteredDebts = _filterDebts(customerDebts);

          return Column(
            children: [
              // الإحصائيات السريعة العامة في الأعلى
              _buildGeneralQuickStats(customerDebts),
              // المحتوى الرئيسي
              Expanded(
                child: filteredDebts.isEmpty
                    ? _buildEmptyState()
                    : _buildDebtsView(filteredDebts),
              ),
            ],
          );
        },
      ),
    );
  }

  // بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بطاقات لهذا العميل',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة بطاقات جديدة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  // فلترة الديون حسب الفلتر المحدد
  List<Debt> _filterDebts(List<Debt> debts) {
    if (_currentFilter == 'all' || _currentFilter == 'table_direct') {
      return debts;
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return debts.where((debt) {
      final debtDueDate =
          DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
      final daysUntilDue = debtDueDate.difference(today).inDays;

      switch (_currentFilter) {
        case 'overdue':
          return daysUntilDue < 0;
        case 'soon':
          return daysUntilDue >= 0 && daysUntilDue <= 3;
        case 'today':
          return debtDueDate.isAtSameMomentAs(today) ||
              debtEntryDate.isAtSameMomentAs(today);
        case 'yesterday':
          final yesterday = today.subtract(const Duration(days: 1));
          return debtEntryDate.isAtSameMomentAs(yesterday);
        case 'thisWeek':
          final weekStart = today.subtract(Duration(days: today.weekday - 1));
          final weekEnd = weekStart.add(const Duration(days: 6));
          return debtEntryDate
                  .isAfter(weekStart.subtract(const Duration(days: 1))) &&
              debtEntryDate.isBefore(weekEnd.add(const Duration(days: 1)));
        case 'thisMonth':
          return debtEntryDate.year == today.year &&
              debtEntryDate.month == today.month;
        default:
          return true;
      }
    }).toList();
  }

  // بناء عرض الديون حسب النوع المحدد
  Widget _buildDebtsView(List<Debt> debts) {
    // إذا كان الفلتر "الكل"، نعرض التصنيف حسب التاريخ
    if (_currentFilter == 'all') {
      return _buildAllDebtsView(debts);
    }

    // للفلاتر الأخرى، نعرض حسب النوع المحدد
    return Column(
      children: [
        const SizedBox(height: 8),
        // الإحصائيات السريعة
        _buildQuickStats(debts),
        const SizedBox(height: 8),
        // المحتوى الرئيسي
        Expanded(
          key: ValueKey(_currentViewType),
          child: _buildViewByType(debts),
        ),
      ],
    );
  }

  // بناء العرض حسب النوع
  Widget _buildViewByType(List<Debt> debts) {
    print('بناء العرض من النوع: $_currentViewType');
    // عرض الجدول فقط
    print('عرض الجدول');
    return _buildTableView(debts);
  }

  // عرض جميع الديون مصنفة حسب التاريخ
  Widget _buildAllDebtsView(List<Debt> debts) {
    // عرض الديون مصنفة حسب الفئات (اليوم، أمس، إلخ)
    final organizedDebts = _organizeDebtsByDate(debts);
    return Column(
      children: [
        const SizedBox(height: 8),
        // الإحصائيات السريعة
        _buildQuickStats(debts),
        const SizedBox(height: 8),
        // المحتوى المصنف
        Expanded(
          child: ListView.builder(
            itemCount: organizedDebts.keys.length,
            itemBuilder: (context, index) {
              final category = organizedDebts.keys.elementAt(index);
              final categoryDebts = organizedDebts[category]!;

              return _buildCategorySection(category, categoryDebts);
            },
          ),
        ),
      ],
    );
  }

  // العرض الجدولي مع الترتيب المحسن
  Widget _buildTableView(List<Debt> debts) {
    // ترتيب البطاقات حسب الأولوية
    debts.sort((a, b) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));

      // حساب حالة البطاقة الأولى
      final aDueDate = DateTime(a.dueDate.year, a.dueDate.month, a.dueDate.day);
      final aEntryDate =
          DateTime(a.entryDate.year, a.entryDate.month, a.entryDate.day);
      final aDaysUntilDue = aDueDate.difference(today).inDays;

      // حساب حالة البطاقة الثانية
      final bDueDate = DateTime(b.dueDate.year, b.dueDate.month, b.dueDate.day);
      final bEntryDate =
          DateTime(b.entryDate.year, b.entryDate.month, b.entryDate.day);
      final bDaysUntilDue = bDueDate.difference(today).inDays;

      // تحديد الأولوية
      final int aPriority =
          _getDebtPriority(aEntryDate, aDaysUntilDue, today, yesterday);
      final int bPriority =
          _getDebtPriority(bEntryDate, bDaysUntilDue, today, yesterday);

      // ترتيب حسب الأولوية، ثم حسب تاريخ القيد (الأحدث أولاً)
      if (aPriority != bPriority) {
        return aPriority.compareTo(bPriority);
      }
      return b.entryDate.compareTo(a.entryDate);
    });

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.zero,
      ),
      child: Column(
        children: [
          // رأس الجدول مع العدادات
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.teal.shade400,
                  Colors.teal.shade600,
                  Colors.teal.shade700,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.zero,
              boxShadow: [
                BoxShadow(
                  color: Colors.teal.withValues(alpha: 0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Column(
              children: [
                // الصف الأول - العنوان وإجمالي البطاقات
                Row(
                  children: [
                    const Icon(Icons.table_chart,
                        color: Colors.white, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      _getTableTitle(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // الصف الثاني - العدادات المصغرة
                _buildMiniCounters(debts),
              ],
            ),
          ),

          // محتوى الجدول
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 4.0,
                child: DataTable(
                  columnSpacing: 4,
                  horizontalMargin: 8,
                  headingRowHeight: 28,
                  dataRowMinHeight: 40,
                  dataRowMaxHeight: 40,
                  headingRowColor:
                      WidgetStateProperty.all(Colors.teal.shade600),
                  border: TableBorder.all(
                    color: Colors.grey.shade300,
                  ),
                  dividerThickness: 1,
                  columns: _buildTableColumns(),
                  rows: _buildTableRows(debts),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // دوال مساعدة
  String _getCardTypeDisplayName(String cardType, BuildContext context) {
    final cardTypeProvider =
        Provider.of<CardTypeProvider>(context, listen: false);

    // البحث في الأنواع الافتراضية أولاً
    try {
      final defaultType = CardType.values.firstWhere(
        (type) => type.name == cardType,
      );
      return defaultType.displayName;
    } catch (e) {
      // إذا لم يوجد في الأنواع الافتراضية، ابحث في الأنواع المخصصة
      try {
        final customType = cardTypeProvider.customCardTypes.firstWhere(
          (type) => 'custom_${type.id}' == cardType,
        );
        return customType.displayName;
      } catch (e) {
        // إذا لم يوجد في أي مكان، أرجع الـ ID كما هو
        return cardType;
      }
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  String _formatDateWithDay(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت'
    ];
    final dayName = dayNames[date.weekday % 7];
    return '$dayName ${DateFormat('dd/MM').format(date)}';
  }

  String _formatFullDateWithDay(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت'
    ];
    final dayName = dayNames[date.weekday % 7];
    final day = DateFormat('dd').format(date);
    final month = DateFormat('MM').format(date);
    final year = DateFormat('yyyy').format(date);
    return '$dayName $year/$month/$day';
  }

  String _formatFullDateWithTime(DateTime date) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت'
    ];
    final dayName = dayNames[date.weekday % 7];
    final day = DateFormat('dd').format(date);
    final month = DateFormat('MM').format(date);
    final year = DateFormat('yyyy').format(date);
    final timeStr = _formatTimeInArabic(date);
    return '$dayName $year/$month/$day 🕐 $timeStr';
  }

  String _formatTimeInArabic(DateTime date) {
    final hour = date.hour;
    final minute = date.minute;

    // تحويل إلى نظام 12 ساعة
    int hour12 = hour;
    if (hour == 0) {
      hour12 = 12;
    } else if (hour > 12) {
      hour12 = hour - 12;
    }

    final minuteStr = minute.toString().padLeft(2, '0');
    final timeStr = '$hour12:$minuteStr';

    // تحديد فترة اليوم بالعربية (كاملة)
    String period;
    if (hour >= 5 && hour < 12) {
      period = 'صباحاً';
    } else if (hour >= 12 && hour < 13) {
      period = 'ظهراً';
    } else if (hour >= 13 && hour < 16) {
      period = 'بعد الظهر';
    } else if (hour >= 16 && hour < 18) {
      period = 'عصراً';
    } else if (hour >= 18 && hour < 20) {
      period = 'مساءً';
    } else if (hour >= 20 && hour < 24) {
      period = 'ليلاً';
    } else {
      period = 'فجراً'; // من منتصف الليل حتى 5 صباحاً
    }

    return '$timeStr $period';
  }

  String _formatFullAmount(double amount) {
    final formatter = NumberFormat('#,###', 'ar');
    return formatter.format(amount.round());
  }

  int _getDaysSinceEntry(Debt debt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final entryDate =
        DateTime(debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
    return today.difference(entryDate).inDays;
  }

  int _getDaysRemaining(Debt debt) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDate =
        DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
    return dueDate.difference(today).inDays;
  }

  String _getTableTitle() {
    switch (_currentFilter) {
      case 'overdue':
        return 'البطاقات المتأخرة';
      case 'soon':
        return 'البطاقات القريبة';
      case 'today':
        return 'بطاقات اليوم';
      case 'yesterday':
        return 'بطاقات أمس';
      case 'thisWeek':
        return 'بطاقات هذا الأسبوع';
      case 'thisMonth':
        return 'بطاقات هذا الشهر';
      case 'table_direct':
        return 'جدول البطاقات';
      default:
        return 'جميع البطاقات';
    }
  }

  // تحديد أولوية البطاقة (الرقم الأقل = أولوية أعلى)
  int _getDebtPriority(DateTime entryDate, int daysUntilDue, DateTime today,
      DateTime yesterday) {
    // 1. بيع اليوم - أولوية عالية جداً (يظهر أولاً)
    if (entryDate.isAtSameMomentAs(today)) return 1;

    // 2. بيع أمس - أولوية عالية
    if (entryDate.isAtSameMomentAs(yesterday)) return 2;

    // 3. بيع هذا الأسبوع - أولوية متوسطة عالية
    if (_isThisWeek(entryDate, today) &&
        !entryDate.isAtSameMomentAs(today) &&
        !entryDate.isAtSameMomentAs(yesterday)) {
      return 3;
    }

    // 4. المتأخرة - أولوية متوسطة
    if (daysUntilDue < 0) return 4;

    // 5. القريبة من الاستحقاق - أولوية متوسطة منخفضة
    if (daysUntilDue >= 0 && daysUntilDue <= 3) return 5;

    // 6. باقي البطاقات - أولوية منخفضة
    return 6;
  }

  bool _isThisWeek(DateTime date, DateTime today) {
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    final weekEnd = weekStart.add(const Duration(days: 6));
    return date.isAfter(weekStart.subtract(const Duration(days: 1))) &&
        date.isBefore(weekEnd.add(const Duration(days: 1)));
  }

  // بناء الإحصائيات السريعة
  Widget _buildQuickStats(List<Debt> debts) {
    final totalAmount =
        debts.fold<double>(0, (sum, debt) => sum + debt.remainingAmount);
    final overdueDebts =
        debts.where((debt) => debt.dueDate.isBefore(DateTime.now())).length;
    final totalCards = debts.length;

    // حساب أنواع الكروت
    final cardTypes = <String, int>{};
    for (final debt in debts) {
      final cardTypeName = _getCardTypeDisplayName(debt.cardType, context);
      cardTypes[cardTypeName] = (cardTypes[cardTypeName] ?? 0) + 1;
    }

    return SizedBox(
      height: 120,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          children: [
            // إجمالي المبلغ
            _buildStatCard(
              'إجمالي المبلغ',
              _formatFullAmount(totalAmount),
              Icons.attach_money,
              Colors.green,
            ),
            const SizedBox(width: 6),

            // إجمالي الكارتات
            _buildStatCard(
              'إجمالي الكارتات',
              '$totalCards',
              Icons.credit_card,
              Colors.blue,
            ),
            const SizedBox(width: 6),

            // متأخرة
            _buildStatCard(
              'متأخرة',
              '$overdueDebts',
              Icons.warning,
              Colors.red,
            ),
            const SizedBox(width: 6),

            // أنواع الكروت
            ...cardTypes.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.only(right: 6),
                child: _buildStatCard(
                  entry.key,
                  '${entry.value}',
                  Icons.style,
                  Colors.purple,
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      width: 120,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // تنسيق المبلغ
  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}م';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}ك';
    } else {
      return amount.toStringAsFixed(0);
    }
  }

  // تنظيم الديون حسب التاريخ مع الترتيب الجديد
  Map<String, List<Debt>> _organizeDebtsByDate(List<Debt> debts) {
    final Map<String, List<Debt>> organized = {};
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    for (final debt in debts) {
      final debtEntryDate = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      final debtDueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      final daysUntilDue = debtDueDate.difference(today).inDays;

      String category;

      // تصنيف حسب الأولوية الجديدة
      if (debtEntryDate.isAtSameMomentAs(today)) {
        category = 'اليوم';
      } else if (debtEntryDate.isAtSameMomentAs(yesterday)) {
        category = 'أمس';
      } else if (_isThisWeek(debtEntryDate, today) &&
          !debtEntryDate.isAtSameMomentAs(today) &&
          !debtEntryDate.isAtSameMomentAs(yesterday)) {
        category = 'هذا الأسبوع';
      } else if (daysUntilDue < 0) {
        category = 'منتهية الصلاحية';
      } else if (daysUntilDue >= 0 && daysUntilDue <= 3) {
        category = 'قريباً';
      } else if (_isThisMonth(debtEntryDate, today)) {
        category = 'هذا الشهر';
      } else {
        category = 'سابقة';
      }

      organized.putIfAbsent(category, () => []).add(debt);
    }

    // ترتيب البطاقات داخل كل فئة حسب نفس منطق الأولوية
    organized.forEach((category, debts) {
      debts.sort((a, b) {
        final aEntryDate =
            DateTime(a.entryDate.year, a.entryDate.month, a.entryDate.day);
        final bEntryDate =
            DateTime(b.entryDate.year, b.entryDate.month, b.entryDate.day);
        final aDaysUntilDue =
            DateTime(a.dueDate.year, a.dueDate.month, a.dueDate.day)
                .difference(today)
                .inDays;
        final bDaysUntilDue =
            DateTime(b.dueDate.year, b.dueDate.month, b.dueDate.day)
                .difference(today)
                .inDays;

        final aPriority =
            _getDebtPriority(aEntryDate, aDaysUntilDue, today, yesterday);
        final bPriority =
            _getDebtPriority(bEntryDate, bDaysUntilDue, today, yesterday);

        if (aPriority != bPriority) {
          return aPriority.compareTo(bPriority);
        }
        return b.entryDate.compareTo(a.entryDate);
      });
    });

    // ترتيب الفئات حسب الأولوية
    final orderedCategories = [
      'اليوم',
      'أمس',
      'هذا الأسبوع',
      'منتهية الصلاحية',
      'قريباً',
      'هذا الشهر',
      'سابقة'
    ];

    final sortedOrganized = <String, List<Debt>>{};
    for (final category in orderedCategories) {
      if (organized.containsKey(category) && organized[category]!.isNotEmpty) {
        sortedOrganized[category] = organized[category]!;
      }
    }

    return sortedOrganized;
  }

  bool _isThisMonth(DateTime date, DateTime today) {
    return date.year == today.year && date.month == today.month;
  }

  // بناء قسم الفئة
  Widget _buildCategorySection(String category, List<Debt> debts) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس الفئة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _getCategoryGradientColors(category),
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.teal.withValues(alpha: 0.2),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Column(
              children: [
                // الصف الأول - العنوان وإجمالي البطاقات
                Row(
                  children: [
                    Icon(
                      _getCategoryIcon(category),
                      color: _getCategoryIconColor(category),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      category,
                      style: TextStyle(
                        color: Colors.teal.shade800,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // الصف الثاني - العدادات المصغرة
                _buildCategoryMiniCounters(debts),
              ],
            ),
          ),
          // محتوى الفئة
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: _buildCategoryContent(debts),
          ),
        ],
      ),
    );
  }

  // بناء محتوى الفئة
  Widget _buildCategoryContent(List<Debt> debts) {
    // ترتيب البطاقات حسب الأولوية
    final sortedDebts = List<Debt>.from(debts);
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    sortedDebts.sort((a, b) {
      final aEntryDate =
          DateTime(a.entryDate.year, a.entryDate.month, a.entryDate.day);
      final bEntryDate =
          DateTime(b.entryDate.year, b.entryDate.month, b.entryDate.day);
      final aDaysUntilDue =
          DateTime(a.dueDate.year, a.dueDate.month, a.dueDate.day)
              .difference(today)
              .inDays;
      final bDaysUntilDue =
          DateTime(b.dueDate.year, b.dueDate.month, b.dueDate.day)
              .difference(today)
              .inDays;

      final aPriority =
          _getDebtPriority(aEntryDate, aDaysUntilDue, today, yesterday);
      final bPriority =
          _getDebtPriority(bEntryDate, bDaysUntilDue, today, yesterday);

      if (aPriority != bPriority) {
        return aPriority.compareTo(bPriority);
      }
      return b.entryDate.compareTo(a.entryDate);
    });

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: MediaQuery.of(context).size.width * 3.2,
        child: DataTable(
          columnSpacing: 4,
          horizontalMargin: 8,
          headingRowHeight: 28,
          dataRowMinHeight: 35,
          dataRowMaxHeight: 35,
          headingRowColor: WidgetStateProperty.all(Colors.teal.shade600),
          border: TableBorder.all(
            color: Colors.grey.shade300,
          ),
          dividerThickness: 1,
          columns: _buildCompactTableColumns(),
          rows: _buildCompactTableRows(sortedDebts),
        ),
      ),
    );
  }

  // الحصول على لون الفئة
  Color _getCategoryColor(String category) {
    switch (category) {
      case 'اليوم':
        return Colors.green.shade600;
      case 'أمس':
        return Colors.blue.shade900;
      case 'هذا الأسبوع':
        return Colors.blue.shade600;
      case 'منتهية الصلاحية':
        return Colors.red.shade600;
      case 'قريباً':
        return Colors.orange.shade600;
      case 'هذا الشهر':
        return Colors.purple.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  // الحصول على الألوان المدرجة للفئة (أبيض مدرج مع أخضر مزرق)
  List<Color> _getCategoryGradientColors(String category) {
    // جميع البطاقات تستخدم نفس التدرج الأبيض-الأخضر المزرق
    return [
      Colors.white,
      Colors.teal.shade50,
      Colors.teal.shade100,
      Colors.teal.shade200,
    ];
  }

  // الحصول على لون الأيقونة حسب الفئة
  Color _getCategoryIconColor(String category) {
    switch (category) {
      case 'اليوم':
        return Colors.green.shade600;
      case 'أمس':
        return Colors.blue.shade900;
      case 'هذا الأسبوع':
        return Colors.blue.shade600;
      case 'منتهية الصلاحية':
        return Colors.red.shade600;
      case 'قريباً':
        return Colors.orange.shade600;
      case 'هذا الشهر':
        return Colors.purple.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  // الحصول على أيقونة الفئة
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'اليوم':
        return Icons.today;
      case 'أمس':
        return Icons.history;
      case 'هذا الأسبوع':
        return Icons.date_range;
      case 'منتهية الصلاحية':
        return Icons.warning;
      case 'قريباً':
        return Icons.schedule;
      case 'هذا الشهر':
        return Icons.calendar_month;
      default:
        return Icons.folder;
    }
  }

  // بناء العدادات المصغرة للجدول
  Widget _buildMiniCounters(List<Debt> debts) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    // حساب العدادات
    int todayCount = 0;
    int yesterdayCount = 0;
    int overdueCount = 0;
    int soonCount = 0;
    int thisWeekCount = 0;
    double todayAmount = 0;
    double yesterdayAmount = 0;
    double overdueAmount = 0;
    double soonAmount = 0;
    double thisWeekAmount = 0;

    for (final debt in debts) {
      final debtEntryDate = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      final debtDueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      final daysUntilDue = debtDueDate.difference(today).inDays;

      if (debtEntryDate.isAtSameMomentAs(today)) {
        todayCount++;
        todayAmount += debt.remainingAmount;
      } else if (debtEntryDate.isAtSameMomentAs(yesterday)) {
        yesterdayCount++;
        yesterdayAmount += debt.remainingAmount;
      } else if (_isThisWeek(debtEntryDate, today)) {
        thisWeekCount++;
        thisWeekAmount += debt.remainingAmount;
      }

      if (daysUntilDue < 0) {
        overdueCount++;
        overdueAmount += debt.remainingAmount;
      } else if (daysUntilDue >= 0 && daysUntilDue <= 3) {
        soonCount++;
        soonAmount += debt.remainingAmount;
      }
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          if (todayCount > 0)
            _buildDateCounter(
                'اليوم', todayCount, Icons.today, Colors.green.shade600),
          if (todayCount > 0) const SizedBox(width: 6),
          if (yesterdayCount > 0)
            _buildDateCounter(
                'أمس', yesterdayCount, Icons.history, Colors.blue.shade900),
          if (yesterdayCount > 0) const SizedBox(width: 6),
          if (thisWeekCount > 0)
            _buildDateCounter('هذا الأسبوع', thisWeekCount, Icons.date_range,
                Colors.blue.shade600),
          if (thisWeekCount > 0) const SizedBox(width: 6),
          if (overdueCount > 0)
            _buildDateCounter(
                'متأخر', overdueCount, Icons.warning, Colors.red.shade600),
          if (overdueCount > 0) const SizedBox(width: 6),
          if (soonCount > 0)
            _buildDateCounter(
                'قريباً', soonCount, Icons.schedule, Colors.orange.shade600),
        ],
      ),
    );
  }

  // بناء عداد التاريخ
  Widget _buildDateCounter(
      String label, int count, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Colors.grey.shade100,
            Colors.grey.shade200,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12),
          const SizedBox(width: 3),
          Text(
            label,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 3),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Text(
              '$count',
              style: const TextStyle(
                color: Colors.black87,
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء العدادات المصغرة للفئات
  Widget _buildCategoryMiniCounters(List<Debt> debts) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    // حساب العدادات
    int todayCount = 0;
    int yesterdayCount = 0;
    int overdueCount = 0;
    int soonCount = 0;
    int thisWeekCount = 0;
    double totalAmount = 0;
    int totalQuantity = 0;
    final Map<String, int> cardTypes = {};

    for (final debt in debts) {
      totalAmount += debt.remainingAmount;
      totalQuantity += debt.quantity;

      // حساب أنواع الكروت
      final cardTypeName = _getCardTypeDisplayName(debt.cardType, context);
      cardTypes[cardTypeName] = (cardTypes[cardTypeName] ?? 0) + 1;

      final debtEntryDate = DateTime(
        debt.entryDate.year,
        debt.entryDate.month,
        debt.entryDate.day,
      );
      final debtDueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );
      final daysUntilDue = debtDueDate.difference(today).inDays;

      if (debtEntryDate.isAtSameMomentAs(today)) {
        todayCount++;
      } else if (debtEntryDate.isAtSameMomentAs(yesterday)) {
        yesterdayCount++;
      } else if (_isThisWeek(debtEntryDate, today)) {
        thisWeekCount++;
      }

      if (daysUntilDue < 0) {
        overdueCount++;
      } else if (daysUntilDue >= 0 && daysUntilDue <= 3) {
        soonCount++;
      }
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          // العدادات الزمنية أولاً
          if (todayCount > 0)
            _buildCategoryCounter('اليوم', '$todayCount', Icons.today),
          if (todayCount > 0) const SizedBox(width: 6),
          if (yesterdayCount > 0)
            _buildCategoryCounter('أمس', '$yesterdayCount', Icons.history),
          if (yesterdayCount > 0) const SizedBox(width: 6),
          if (thisWeekCount > 0)
            _buildCategoryCounter(
                'الأسبوع', '$thisWeekCount', Icons.date_range),
          if (thisWeekCount > 0) const SizedBox(width: 6),
          if (overdueCount > 0)
            _buildCategoryCounter('متأخر', '$overdueCount', Icons.warning),
          if (overdueCount > 0) const SizedBox(width: 6),
          if (soonCount > 0)
            _buildCategoryCounter('قريباً', '$soonCount', Icons.schedule),
          if (soonCount > 0) const SizedBox(width: 6),

          // فاصل بين العدادات الزمنية والإحصائية
          if ((todayCount > 0 ||
                  yesterdayCount > 0 ||
                  thisWeekCount > 0 ||
                  overdueCount > 0 ||
                  soonCount > 0) &&
              (totalAmount > 0 || totalQuantity > 0 || cardTypes.isNotEmpty))
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 6),
              height: 20,
              width: 1,
              color: Colors.grey.withValues(alpha: 0.4),
            ),

          // العدادات الإحصائية
          if (totalAmount > 0) ...[
            _buildCategoryCounter(
                'المبلغ', _formatFullAmount(totalAmount), Icons.attach_money),
            const SizedBox(width: 6),
          ],

          if (totalQuantity > 0) ...[
            _buildCategoryCounter('الكمية', '$totalQuantity', Icons.inventory),
            const SizedBox(width: 6),
          ],

          // أنواع الكروت
          ...cardTypes.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.only(left: 6),
              child: _buildCategoryCounter(
                  entry.key, '${entry.value}', Icons.credit_card),
            );
          }),
        ],
      ),
    );
  }

  // بناء عداد الفئة
  Widget _buildCategoryCounter(String label, String count, IconData icon) {
    // تحديد لون الأيقونة حسب النوع
    Color iconColor;

    switch (icon) {
      case Icons.today:
        iconColor = Colors.green.shade600;
        break;
      case Icons.history:
        iconColor = Colors.blue.shade900;
        break;
      case Icons.date_range:
        iconColor = Colors.blue.shade600;
        break;
      case Icons.warning:
        iconColor = Colors.red.shade600;
        break;
      case Icons.schedule:
        iconColor = Colors.orange.shade600;
        break;
      case Icons.attach_money:
        iconColor = Colors.green.shade700;
        break;
      case Icons.inventory:
        iconColor = Colors.purple.shade600;
        break;
      case Icons.credit_card:
        iconColor = Colors.indigo.shade600;
        break;
      default:
        iconColor = Colors.grey.shade600;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Colors.grey.shade100,
            Colors.grey.shade200,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: iconColor, size: 12),
          const SizedBox(width: 3),
          Text(
            label,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 3),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Text(
              count,
              style: const TextStyle(
                color: Colors.black87,
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء أعمدة الجدول المضغوط للفئات
  List<DataColumn> _buildCompactTableColumns() {
    return [
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'نوع الكارت',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 11,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الكمية',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 11,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'المبلغ',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 11,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'تاريخ القيد',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 11,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الاستحقاق',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 11,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      DataColumn(
        label: Container(
          width: 40,
          alignment: Alignment.center,
          child: const Text(
            'الحالة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 11,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      DataColumn(
        label: Container(
          width: 120,
          alignment: Alignment.center,
          child: const Text(
            'الملاحظات',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 11,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'منذ القيد',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 11,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'المتبقي',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 11,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    ];
  }

  // بناء صفوف الجدول المضغوط للفئات
  List<DataRow> _buildCompactTableRows(List<Debt> debts) {
    return debts.map((debt) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final debtDueDate =
          DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);

      final daysUntilDue = debtDueDate.difference(today).inDays;
      final isOverdue = daysUntilDue < 0;
      final isSoon = !isOverdue && daysUntilDue >= 0 && daysUntilDue <= 3;
      final isYesterday = debtEntryDate
          .isAtSameMomentAs(today.subtract(const Duration(days: 1)));
      final isToday = debtEntryDate.isAtSameMomentAs(today);

      Color rowColor = Colors.white;
      if (isOverdue) {
        rowColor = Colors.red.shade600;
      } else if (isSoon) {
        rowColor = Colors.orange.shade400;
      } else if (isToday) {
        rowColor = Colors.green.shade600;
      } else if (isYesterday) {
        rowColor = Colors.blue.shade900;
      }

      return DataRow(
        color: WidgetStateProperty.all(rowColor),
        cells: [
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                _getCardTypeDisplayName(debt.cardType, context),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.black87,
                ),
                textAlign: TextAlign.center,
                softWrap: false,
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                '${debt.quantity}',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                _formatFullAmount(debt.amount),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.green.shade700,
                ),
                textAlign: TextAlign.center,
                softWrap: false,
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                _formatFullDateWithTime(debt.entryDate),
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.black87,
                ),
                textAlign: TextAlign.center,
                softWrap: false,
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                _formatFullDateWithDay(debt.dueDate),
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.black87,
                ),
                textAlign: TextAlign.center,
                softWrap: false,
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                isOverdue
                    ? 'متأخر'
                    : isSoon
                        ? 'قريباً'
                        : isToday
                            ? 'اليوم'
                            : isYesterday
                                ? 'أمس'
                                : 'نشط',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          DataCell(
            Container(
              width: 120,
              alignment: Alignment.center,
              child: Center(
                child: Text(
                  debt.notes?.isNotEmpty == true ? debt.notes! : '-',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                '${_getDaysSinceEntry(debt)} يوم',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                '${_getDaysRemaining(debt)} يوم',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      );
    }).toList();
  }

  // بناء أعمدة الجدول
  List<DataColumn> _buildTableColumns() {
    return [
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'نوع الكارت',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الكمية',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'المبلغ',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                fontFamily: 'Calibri',
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'تاريخ القيد',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'الاستحقاق',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      DataColumn(
        label: Container(
          width: 60,
          alignment: Alignment.center,
          child: const Text(
            'الحالة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      DataColumn(
        label: Container(
          width: 200,
          alignment: Alignment.center,
          child: const Text(
            'الملاحظات',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'منذ القيد',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
      const DataColumn(
        label: Expanded(
          child: Center(
            child: Text(
              'المتبقي',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    ];
  }

  // بناء صفوف الجدول
  List<DataRow> _buildTableRows(List<Debt> debts) {
    return debts.map((debt) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final debtDueDate =
          DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);

      final daysUntilDue = debtDueDate.difference(today).inDays;
      final isOverdue = daysUntilDue < 0;
      final isSoon = !isOverdue && daysUntilDue >= 0 && daysUntilDue <= 3;
      final isYesterday = debtEntryDate
          .isAtSameMomentAs(today.subtract(const Duration(days: 1)));
      final isToday = debtEntryDate.isAtSameMomentAs(today);

      Color rowColor = Colors.white;
      if (isOverdue) {
        rowColor = Colors.red.shade600;
      } else if (isSoon) {
        rowColor = Colors.orange.shade400;
      } else if (isToday) {
        rowColor = Colors.green.shade600;
      } else if (isYesterday) {
        rowColor = Colors.blue.shade900;
      }

      return DataRow(
        color: WidgetStateProperty.all(rowColor),
        cells: [
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                _getCardTypeDisplayName(debt.cardType, context),
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.black87,
                ),
                textAlign: TextAlign.center,
                softWrap: false,
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                '${debt.quantity}',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                _formatFullAmount(debt.amount),
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                  color: isOverdue || isYesterday || isToday
                      ? Colors.white
                      : Colors.green.shade700,
                ),
                textAlign: TextAlign.center,
                softWrap: false,
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Center(
                child: Text(
                  _formatFullDateWithTime(debt.entryDate),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  softWrap: false,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Center(
                child: Text(
                  _formatFullDateWithDay(debt.dueDate),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  softWrap: false,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Center(
                child: Text(
                  isOverdue
                      ? 'متأخر'
                      : isSoon
                          ? 'قريباً'
                          : isToday
                              ? 'اليوم'
                              : isYesterday
                                  ? 'أمس'
                                  : 'نشط',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              width: 200,
              alignment: Alignment.center,
              child: Center(
                child: Text(
                  debt.notes?.isNotEmpty == true ? debt.notes! : '-',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Center(
                child: Text(
                  '${_getDaysSinceEntry(debt)} يوم',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          DataCell(
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Center(
                child: Text(
                  '${_getDaysRemaining(debt)} يوم',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: isOverdue || isYesterday || isToday
                        ? Colors.white
                        : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      );
    }).toList();
  }

  // بناء أزرار الفلترة في الشريط العلوي
  Widget _buildFilterChips() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.filter_list, color: Colors.white),
      onSelected: (String value) {
        setState(() {
          _currentFilter = value;
        });
      },
      itemBuilder: (BuildContext context) => [
        const PopupMenuItem<String>(
          value: 'all',
          child: Text('الكل'),
        ),
        const PopupMenuItem<String>(
          value: 'table_direct',
          child: Text('جدول مباشر'),
        ),
        const PopupMenuItem<String>(
          value: 'overdue',
          child: Text('متأخر'),
        ),
        const PopupMenuItem<String>(
          value: 'soon',
          child: Text('قريباً'),
        ),
        const PopupMenuItem<String>(
          value: 'today',
          child: Text('اليوم'),
        ),
        const PopupMenuItem<String>(
          value: 'yesterday',
          child: Text('أمس'),
        ),
        const PopupMenuItem<String>(
          value: 'thisWeek',
          child: Text('هذا الأسبوع'),
        ),
        const PopupMenuItem<String>(
          value: 'thisMonth',
          child: Text('هذا الشهر'),
        ),
      ],
    );
  }

  // بناء شريط الفلترة العلوي
  Widget _buildTopFilterBar() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip('الكل', 'all'),
          const SizedBox(width: 8),
          _buildFilterChip('جدول مباشر', 'table_direct'),
          const SizedBox(width: 8),
          _buildFilterChip('متأخر', 'overdue'),
          const SizedBox(width: 8),
          _buildFilterChip('قريباً', 'soon'),
          const SizedBox(width: 8),
          _buildFilterChip('اليوم', 'today'),
          const SizedBox(width: 8),
          _buildFilterChip('أمس', 'yesterday'),
          const SizedBox(width: 8),
          _buildFilterChip('هذا الأسبوع', 'thisWeek'),
          const SizedBox(width: 8),
          _buildFilterChip('هذا الشهر', 'thisMonth'),
        ],
      ),
    );
  }

  // بناء رقاقة الفلتر
  Widget _buildFilterChip(String label, String value) {
    final isSelected = _currentFilter == value;
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.white70,
          fontSize: 12,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _currentFilter = value;
        });
      },
      backgroundColor: Colors.transparent,
      selectedColor: Colors.white.withValues(alpha: 0.2),
      side: BorderSide(
        color: isSelected ? Colors.white : Colors.white54,
      ),
      showCheckmark: false,
    );
  }

  // بناء الإحصائيات السريعة العامة
  Widget _buildGeneralQuickStats(List<Debt> allDebts) {
    if (allDebts.isEmpty) return const SizedBox.shrink();

    final totalAmount =
        allDebts.fold<double>(0, (sum, debt) => sum + debt.remainingAmount);
    final overdueDebts =
        allDebts.where((debt) => debt.dueDate.isBefore(DateTime.now())).length;
    final totalCards = allDebts.length;
    final totalQuantity =
        allDebts.fold<int>(0, (sum, debt) => sum + debt.quantity);

    // حساب أنواع الكروت
    final cardTypes = <String, int>{};
    for (final debt in allDebts) {
      final cardTypeName = _getCardTypeDisplayName(debt.cardType, context);
      cardTypes[cardTypeName] = (cardTypes[cardTypeName] ?? 0) + 1;
    }

    // حساب الإحصائيات الزمنية
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    int todayCount = 0;
    int yesterdayCount = 0;
    int soonCount = 0;
    double todayAmount = 0;
    double yesterdayAmount = 0;
    double soonAmount = 0;

    for (final debt in allDebts) {
      final debtEntryDate = DateTime(
          debt.entryDate.year, debt.entryDate.month, debt.entryDate.day);
      final debtDueDate =
          DateTime(debt.dueDate.year, debt.dueDate.month, debt.dueDate.day);
      final daysUntilDue = debtDueDate.difference(today).inDays;

      if (debtEntryDate.isAtSameMomentAs(today)) {
        todayCount++;
        todayAmount += debt.remainingAmount;
      } else if (debtEntryDate.isAtSameMomentAs(yesterday)) {
        yesterdayCount++;
        yesterdayAmount += debt.remainingAmount;
      }

      if (daysUntilDue >= 0 && daysUntilDue <= 3) {
        soonCount++;
        soonAmount += debt.remainingAmount;
      }
    }

    return Container(
      margin: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الإحصائيات
          Padding(
            padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
            child: Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  size: 16,
                  color: Colors.teal.shade600,
                ),
                const SizedBox(width: 6),
                Text(
                  'إحصائيات عامة',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal.shade700,
                  ),
                ),
              ],
            ),
          ),
          // الإحصائيات مع التمرير الأفقي
          SizedBox(
            height: 100,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                children: [
                  // إجمالي المبلغ
                  _buildGeneralStatCard(
                    'إجمالي المبلغ',
                    _formatFullAmount(totalAmount),
                    Icons.attach_money,
                    Colors.green,
                  ),
                  const SizedBox(width: 8),

                  // إجمالي الكارتات
                  _buildGeneralStatCard(
                    'إجمالي الكارتات',
                    '$totalCards',
                    Icons.credit_card,
                    Colors.blue,
                  ),
                  const SizedBox(width: 8),

                  // إجمالي الكمية
                  _buildGeneralStatCard(
                    'إجمالي الكمية',
                    '$totalQuantity',
                    Icons.inventory,
                    Colors.purple,
                  ),
                  const SizedBox(width: 8),

                  // متأخرة
                  if (overdueDebts > 0) ...[
                    _buildGeneralStatCard(
                      'متأخرة',
                      '$overdueDebts',
                      Icons.warning,
                      Colors.red,
                    ),
                    const SizedBox(width: 8),
                  ],

                  // اليوم
                  if (todayCount > 0) ...[
                    _buildGeneralStatCard(
                      'اليوم',
                      '$todayCount',
                      Icons.today,
                      Colors.green,
                      subtitle: _formatFullAmount(todayAmount),
                    ),
                    const SizedBox(width: 8),
                  ],

                  // أمس
                  if (yesterdayCount > 0) ...[
                    _buildGeneralStatCard(
                      'أمس',
                      '$yesterdayCount',
                      Icons.history,
                      Colors.blue,
                      subtitle: _formatFullAmount(yesterdayAmount),
                    ),
                    const SizedBox(width: 8),
                  ],

                  // قريباً
                  if (soonCount > 0) ...[
                    _buildGeneralStatCard(
                      'قريباً',
                      '$soonCount',
                      Icons.schedule,
                      Colors.orange,
                      subtitle: _formatFullAmount(soonAmount),
                    ),
                    const SizedBox(width: 8),
                  ],

                  // أنواع الكروت
                  ...cardTypes.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: _buildGeneralStatCard(
                        entry.key,
                        '${entry.value}',
                        Icons.style,
                        Colors.indigo,
                      ),
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائية عامة
  Widget _buildGeneralStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      width: 120,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.teal.shade50, Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 9,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}
